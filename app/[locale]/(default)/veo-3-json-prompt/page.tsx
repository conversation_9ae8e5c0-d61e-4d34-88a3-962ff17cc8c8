import { getTranslations } from "next-intl/server";
import Veo3JsonGenerator from "@/components/ui/veo-3-json-generator";
import Hero from "@/components/blocks/hero";
import { getPage } from "@/services/page";
import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Pricing from "@/components/blocks/pricing";
import Stats from "@/components/blocks/stats";
import PromptExamples from "@/components/blocks/prompt-examples";
import Showcase from "@/components/blocks/showcase";
import Testimonial from "@/components/blocks/testimonial";
import { V3PromptGeneratorPage } from "@/types/pages/veo3-prompt-generator";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/veo-3-json-prompt`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/veo-3-json-prompt`;
  }

  const t = await getTranslations("veo3_json_generator");

  return {
    title: t("title"),
    description: t("description"),
    keywords: "veo-3, json prompt, video generation, ai prompt generator",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function Veo3JsonPromptPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = (await getPage("veo-3-json-prompt", locale)) as V3PromptGeneratorPage
  const t = await getTranslations("veo3_json_generator");

  const examplesConfig = {
    copyButton: t("copyButton"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
  };

  const config = {
    title: t("title"),
    subtitle: t("subtitle"),
    templateMode: t("templateMode"),
    aiMode: t("aiMode"),
    selectTemplate: t("selectTemplate"),
    generateJson: t("generateJson"),
    copyJson: t("copyJson"),
    inputPrompt: t("inputPrompt"),
    generateButton: t("generateButton"),
    generating: t("generating"),
    jsonPreview: t("jsonPreview"),
    sampleVideo: t("sampleVideo"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
    errorNoInput: t("errorNoInput"),
    errorGenerateFailed: t("errorGenerateFailed"),
    successGenerated: t("successGenerated"),
  };

  return (
    <>
    {page.hero && <Hero hero={page.hero} />}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold mb-4 text-foreground">{config.title}</h1>
              <p className="text-xl text-muted-foreground">{config.subtitle}</p>
            </div>

            <Veo3JsonGenerator config={config} />
          </div>
        </div>
      </section>
      {page.branding && <Branding section={page.branding} />}
      {page.examples && (
        <PromptExamples 
          section={page.examples}
          config={examplesConfig}
        />
      )}
      {page.usage && <Feature3 section={page.usage} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
